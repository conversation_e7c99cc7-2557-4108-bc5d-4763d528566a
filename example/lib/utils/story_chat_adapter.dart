import '../models/story.dart';
import '../models/chat_session.dart';
import '../models/message.dart';
import '../models/plot.dart';
import '../utils/plot_parser.dart';

class StoryChatAdapter {
  /// 将Story对象转换为ChatSession
  static ChatSession createChatSessionFromStory(Story story, String locale) {
    final title = _getLocalizedString(story.title, locale);
    final plotText = _getLocalizedString(story.currentPlot, locale);

    final String baseId = DateTime.now().millisecondsSinceEpoch.toString();
    final List<Message> initialMessages = [];

    if (story.presetPlot != null) {
      // 使用预设的首段剧情（标准 Plot JSON）构造首屏消息
      try {
        final plot = Plot.fromJson(story.presetPlot!);
        final parseResult = PlotParseResult.fromPlot(plot, isStandardJson: true);
        final messageResult = PlotParser.generateMessagesFromPlot(parseResult, baseId);
        initialMessages.addAll(messageResult.messages);
      } catch (e) {
        // 兜底：如果解析失败，回退到纯文本首条剧情
        initialMessages.add(
          Message.plotMessage(
            id: 'initial_${story.id}_$baseId',
            content: plotText,
          ),
        );
      }
    } else {
      // 无预设剧情时，仍然使用原有首条剧情文案
      initialMessages.add(
        Message.plotMessage(
          id: 'initial_${story.id}_$baseId',
          content: plotText,
        ),
      );
    }

    return ChatSession(
      id: 'story_${story.id}_$baseId',
      title: title,
      avatarUrl: story.imageUrl,
      messages: initialMessages,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      storyId: story.id,
    );
  }

  /// 获取本地化的字符串
  static String _getLocalizedString(Map<String, String> localizedMap, String locale) {
    return localizedMap[locale] ?? localizedMap['zh'] ?? localizedMap['en'] ?? '';
  }
}